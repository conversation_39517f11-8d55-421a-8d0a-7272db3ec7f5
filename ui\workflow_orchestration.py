"""
Workflow Orchestration Module for EEI Cross-Correlation Analysis

This module contains the main workflow orchestration functions for EEI analysis,
including individual well analysis, merged well analysis, and the main application
entry point. These functions coordinate between all other modules to provide
the complete analysis workflow.

Phase 3B: Workflow Orchestration Module
Status: Implementation in progress
"""

import numpy as np
import matplotlib.pyplot as plt
import tkinter as tk
from tkinter import simpledialog, messagebox
import pandas as pd
import logging

# Import backend calculation functions
from eei_calculation_engine import (
    calculate_eei_optimum_angle, calculate_cpei_optimum_parameters,
    calculate_peil_optimum_parameters, nanaware_corrcoef
)
from eei_data_processing import find_nearest_index
from eeimpcalc import eeimpcalc
from eei_config import LOG_KEYWORDS

# Import modular UI components
from ui.plotting_components import (
    plot_correlation_vs_angle, plot_correlation_heatmap, plot_eei_vs_target
)
from ui.dialog_systems import (
    select_alternative_mnemonic, get_analysis_type_and_parameters,
    get_depth_ranges, get_target_log, show_next_action_dialog
)
from ui.file_management import (
    load_multiple_las_files, validate_essential_logs, generate_validation_report,
    categorize_log_curves, display_log_inventory, log_available_curves,
    find_default_columns, analyze_log_availability, load_excel_depth_ranges
)
from ui.calculator_interface import get_calculations_for_eei
from ui.helper_functions import safe_format_float, safe_format_parameter_string

# Set up logging
logger = logging.getLogger(__name__)

# Import additional calculation functions that are still in main file
# These will be imported from the main module until they are also modularized
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class WorkflowOrchestrator:
    """
    Main workflow orchestrator for EEI cross-correlation analysis.

    This class provides the main workflow functions for individual well analysis,
    merged well analysis, and the complete application workflow.
    """

    def __init__(self):
        """Initialize the workflow orchestrator."""
        self.logger = logging.getLogger(__name__)

    def individual_well_analysis(self, las_files, log_keywords_for_finding_cols, target_log_generic_name,
                                depth_ranges, analysis_method, calcmethod, k_method, k_value,
                                alternative_mnemonics=None):
        """
        Perform individual well analysis for EEI, CPEI, or PEIL.

        Args:
            las_files: List of LAS file objects
            log_keywords_for_finding_cols: Dictionary mapping generic names to possible mnemonics
            target_log_generic_name: Generic name of the target log
            depth_ranges: Dictionary mapping well names to (top_depth, bottom_depth) tuples
            analysis_method: 1=EEI, 2=CPEI, 3=PEIL
            calcmethod: Method to use for EEI calculation (only used for EEI)
            k_method: Method for k value (only used for EEI)
            k_value: Constant k value (only used for EEI)
            alternative_mnemonics: Dictionary to track alternative mnemonics selected by the user

        Returns:
            Tuple of (all_wells_results, all_wells_data)
        """
        # Import additional functions from main module that haven't been modularized yet
        from load_multilas_EEI_XCOR_PLOT_Final import (
            calculate_eei, calculate_cpei_for_plotting, calculate_peil_for_plotting,
            validate_cpei_peil_inputs, calculate_cpei, calculate_peil
        )

        all_wells_results = []
        all_wells_data = []

        # Initialize alternative_mnemonics if not provided
        if alternative_mnemonics is None:
            alternative_mnemonics = {}

        required_base_generic_logs = ['DEPTH', 'DT', 'DTS', 'RHOB']

        for las in las_files:
            well_name = las.well.WELL.value
            top_depth, bottom_depth = depth_ranges[well_name]

            # Find actual mnemonics for this specific well
            current_well_columns = find_default_columns(las, log_keywords_for_finding_cols)

            actual_mnemonics_for_base_logs = {}
            missing_base_logs_info = []
            for generic_name in required_base_generic_logs:
                actual_mnemonic = current_well_columns.get(generic_name)
                if actual_mnemonic is None or actual_mnemonic not in las.curves:
                    missing_base_logs_info.append(f"{generic_name} (searched aliases: {log_keywords_for_finding_cols.get(generic_name, [])})")
                else:
                    actual_mnemonics_for_base_logs[generic_name] = actual_mnemonic

            if missing_base_logs_info:
                print(f"Warning: Missing or invalid base logs ({', '.join(missing_base_logs_info)}) for well {well_name}. Skipping this well for individual analysis.")
                all_wells_data.append({'well_name': well_name, 'depth': None, 'target': None, 'normalized_eei': None, 'angle': None, 'vol_wetclay': None})
                all_wells_results.append({'well_name': well_name, 'optimum_angle': None, 'max_correlation': None, 'top_depth': top_depth, 'bottom_depth': bottom_depth})
                continue

            target_log_actual_mnemonic = current_well_columns.get(target_log_generic_name)
            # If generic name itself is a curve (e.g. user typed 'GR' and 'GR' exists, but 'GR' is not in log_keywords['GR'])
            if target_log_actual_mnemonic is None and target_log_generic_name in las.curves:
                target_log_actual_mnemonic = target_log_generic_name
                print(f"Info: Target log '{target_log_generic_name}' used directly as mnemonic for well {well_name}.")

            # Check if target log is missing and offer interactive fallback selection
            if target_log_actual_mnemonic is None or target_log_actual_mnemonic not in las.curves:
                # Check if we already have an alternative for this well
                well_key = f"{well_name}:{target_log_generic_name}"
                if well_key in alternative_mnemonics:
                    alternative = alternative_mnemonics[well_key]
                    if alternative is not None:
                        target_log_actual_mnemonic = alternative
                        print(f"Info: Using previously selected alternative '{alternative}' for target log '{target_log_generic_name}' in well '{well_name}'.")
                    else:
                        print(f"Info: Well '{well_name}' was previously marked to be skipped for target log '{target_log_generic_name}'.")
                        all_wells_data.append({'well_name': well_name, 'depth': None, 'target': None, 'normalized_eei': None, 'angle': None, 'vol_wetclay': None})
                        all_wells_results.append({'well_name': well_name, 'optimum_angle': None, 'max_correlation': None, 'top_depth': top_depth, 'bottom_depth': bottom_depth})
                        continue
                else:
                    # Display dialog for selecting an alternative
                    print(f"Warning: Target log '{target_log_generic_name}' (resolved mnemonic '{target_log_actual_mnemonic or 'None'}', searched aliases: {log_keywords_for_finding_cols.get(target_log_generic_name, [target_log_generic_name])}) not found in well {well_name}.")
                    alternative = select_alternative_mnemonic(las, target_log_generic_name, well_name)

                    # Store the selected alternative (or None if skipped)
                    alternative_mnemonics[well_key] = alternative

                    if alternative is None:
                        print(f"Info: User chose to skip well '{well_name}' for target log '{target_log_generic_name}'.")
                        all_wells_data.append({'well_name': well_name, 'depth': None, 'target': None, 'normalized_eei': None, 'angle': None, 'vol_wetclay': None})
                        all_wells_results.append({'well_name': well_name, 'optimum_angle': None, 'max_correlation': None, 'top_depth': top_depth, 'bottom_depth': bottom_depth})
                        continue
                    else:
                        target_log_actual_mnemonic = alternative
                        print(f"Info: Using alternative '{alternative}' for target log '{target_log_generic_name}' in well '{well_name}'.")

            vcl_actual_mnemonic = current_well_columns.get('VCL') # VCL is optional, might be None. find_default_columns would have printed if not found.

            # Perform optimization based on analysis method
            if analysis_method == 1:  # EEI Analysis
                optimum_angle, max_correlation, angles, correlations = calculate_eei_optimum_angle(
                    las, actual_mnemonics_for_base_logs, target_log_actual_mnemonic,
                    top_depth, bottom_depth, calcmethod, k_method, k_value
                )

                if optimum_angle is None or max_correlation is None:
                    print(f"Skipping well {well_name} due to invalid data from EEI optimum angle calculation.")
                    all_wells_data.append({'well_name': well_name, 'depth': None, 'target': None, 'normalized_eei': None, 'angle': None, 'vol_wetclay': None})
                    all_wells_results.append({'well_name': well_name, 'optimum_angle': None, 'max_correlation': None, 'top_depth': top_depth, 'bottom_depth': bottom_depth})
                    continue

                print(f"Optimum angle for Well {well_name}: {optimum_angle}°")
                print(f"Maximum correlation coefficient with {target_log_generic_name} (using {target_log_actual_mnemonic or 'None'}): {safe_format_float(max_correlation, precision=4, default='N/A')}")

                # Plot EEI-Target Log Correlation vs Angle using modular plotting component
                plot_correlation_vs_angle(angles, correlations, optimum_angle, target_log_generic_name,
                                        well_name, top_depth, bottom_depth)

                # Calculate EEI using the optimum angle
                depth, target, normalized_eei, vol_wetclay = calculate_eei(
                    las, actual_mnemonics_for_base_logs, target_log_actual_mnemonic, vcl_actual_mnemonic,
                    top_depth, bottom_depth, optimum_angle, calcmethod, k_method, k_value
                )

                all_wells_results.append({
                    'well_name': well_name,
                    'optimum_angle': optimum_angle,
                    'max_correlation': max_correlation,
                    'top_depth': top_depth,
                    'bottom_depth': bottom_depth
                })

                all_wells_data.append({
                    'well_name': well_name,
                    'depth': depth,
                    'target': target,
                    'normalized_eei': normalized_eei,
                    'angle': optimum_angle,
                    'vol_wetclay': vol_wetclay
                })

            elif analysis_method == 2:  # CPEI Analysis
                optimal_n, optimal_phi, max_correlation, n_values, phi_values, correlation_matrix = calculate_cpei_optimum_parameters(
                    las, actual_mnemonics_for_base_logs, target_log_actual_mnemonic, top_depth, bottom_depth
                )

                if optimal_n is None or optimal_phi is None or max_correlation is None:
                    print(f"Skipping well {well_name} due to invalid data from CPEI optimization.")
                    all_wells_data.append({'well_name': well_name, 'depth': None, 'target': None, 'normalized_eei': None, 'angle': None, 'vol_wetclay': None})
                    all_wells_results.append({'well_name': well_name, 'optimum_angle': None, 'max_correlation': None, 'top_depth': top_depth, 'bottom_depth': bottom_depth})
                    continue

                # Safely format CPEI parameters with None value checks
                try:
                    n_str = f"{optimal_n:.1f}" if optimal_n is not None else "N/A"
                    phi_str = f"{optimal_phi}°" if optimal_phi is not None else "N/A"
                    print(f"Optimal CPEI parameters for Well {well_name}: n={n_str}, phi={phi_str}")
                except (ValueError, TypeError) as e:
                    print(f"Error formatting CPEI parameters for Well {well_name}: optimal_n={optimal_n}, optimal_phi={optimal_phi}. Error: {str(e)}")
                    print(f"Optimal CPEI parameters for Well {well_name}: n={optimal_n}, phi={optimal_phi}")

                # Ensure max_correlation is properly formatted as a float
                try:
                    correlation_value = float(max_correlation) if max_correlation is not None else 0.0
                    print(f"Maximum correlation coefficient with {target_log_generic_name} (using {target_log_actual_mnemonic or 'None'}): {correlation_value:.4f}")
                except (ValueError, TypeError) as e:
                    print(f"Error formatting correlation value: {max_correlation}. Error: {str(e)}")
                    print(f"Maximum correlation coefficient with {target_log_generic_name} (using {target_log_actual_mnemonic or 'None'}): {max_correlation}")

                # Plot CPEI correlation heatmap using modular plotting component
                plot_correlation_heatmap(correlation_matrix, phi_values, n_values, optimal_n, optimal_phi,
                                       max_correlation, target_log_generic_name, 'CPEI', well_name,
                                       top_depth, bottom_depth)

                # For CPEI, we'll store the parameters differently since there's no single "angle"
                # Safely format parameter strings for storage
                try:
                    angle_str = f"n={optimal_n:.1f}, phi={optimal_phi}°" if optimal_n is not None and optimal_phi is not None else f"n={optimal_n}, phi={optimal_phi}"
                except (ValueError, TypeError):
                    angle_str = f"n={optimal_n}, phi={optimal_phi}"

                all_wells_results.append({
                    'well_name': well_name,
                    'optimum_angle': angle_str,  # Store as string for compatibility
                    'max_correlation': max_correlation,
                    'top_depth': top_depth,
                    'bottom_depth': bottom_depth
                })

                # Calculate CPEI using optimal parameters for plotting
                depth, target, normalized_cpei, vol_wetclay = calculate_cpei_for_plotting(
                    las, actual_mnemonics_for_base_logs, target_log_actual_mnemonic, vcl_actual_mnemonic,
                    top_depth, bottom_depth, optimal_n, optimal_phi
                )

                all_wells_data.append({
                    'well_name': well_name,
                    'depth': depth,
                    'target': target,
                    'normalized_eei': normalized_cpei,  # Use normalized_eei field for compatibility
                    'angle': angle_str,
                    'vol_wetclay': vol_wetclay
                })

            elif analysis_method == 3:  # PEIL Analysis
                optimal_n, optimal_phi, max_correlation, n_values, phi_values, correlation_matrix = calculate_peil_optimum_parameters(
                    las, actual_mnemonics_for_base_logs, target_log_actual_mnemonic, top_depth, bottom_depth
                )

                if optimal_n is None or optimal_phi is None or max_correlation is None:
                    print(f"Skipping well {well_name} due to invalid data from PEIL optimization.")
                    all_wells_data.append({'well_name': well_name, 'depth': None, 'target': None, 'normalized_eei': None, 'angle': None, 'vol_wetclay': None})
                    all_wells_results.append({'well_name': well_name, 'optimum_angle': None, 'max_correlation': None, 'top_depth': top_depth, 'bottom_depth': bottom_depth})
                    continue

                # Safely format PEIL parameters with None value checks
                try:
                    n_str = f"{optimal_n:.1f}" if optimal_n is not None else "N/A"
                    phi_str = f"{optimal_phi}°" if optimal_phi is not None else "N/A"
                    print(f"Optimal PEIL parameters for Well {well_name}: n={n_str}, phi={phi_str}")
                except (ValueError, TypeError) as e:
                    print(f"Error formatting PEIL parameters for Well {well_name}: optimal_n={optimal_n}, optimal_phi={optimal_phi}. Error: {str(e)}")
                    print(f"Optimal PEIL parameters for Well {well_name}: n={optimal_n}, phi={optimal_phi}")

                # Ensure max_correlation is properly formatted as a float
                try:
                    correlation_value = float(max_correlation) if max_correlation is not None else 0.0
                    print(f"Maximum correlation coefficient with {target_log_generic_name} (using {target_log_actual_mnemonic or 'None'}): {correlation_value:.4f}")
                except (ValueError, TypeError) as e:
                    print(f"Error formatting correlation value: {max_correlation}. Error: {str(e)}")
                    print(f"Maximum correlation coefficient with {target_log_generic_name} (using {target_log_actual_mnemonic or 'None'}): {max_correlation}")

                # Plot PEIL correlation heatmap using modular plotting component
                plot_correlation_heatmap(correlation_matrix, phi_values, n_values, optimal_n, optimal_phi,
                                       max_correlation, target_log_generic_name, 'PEIL', well_name,
                                       top_depth, bottom_depth)

                # For PEIL, we'll store the parameters differently since there's no single "angle"
                # Safely format parameter strings for storage
                try:
                    angle_str = f"n={optimal_n:.1f}, phi={optimal_phi}°" if optimal_n is not None and optimal_phi is not None else f"n={optimal_n}, phi={optimal_phi}"
                except (ValueError, TypeError):
                    angle_str = f"n={optimal_n}, phi={optimal_phi}"

                all_wells_results.append({
                    'well_name': well_name,
                    'optimum_angle': angle_str,  # Store as string for compatibility
                    'max_correlation': max_correlation,
                    'top_depth': top_depth,
                    'bottom_depth': bottom_depth
                })

                # Calculate PEIL using optimal parameters for plotting
                depth, target, normalized_peil, vol_wetclay = calculate_peil_for_plotting(
                    las, actual_mnemonics_for_base_logs, target_log_actual_mnemonic, vcl_actual_mnemonic,
                    top_depth, bottom_depth, optimal_n, optimal_phi
                )

                all_wells_data.append({
                    'well_name': well_name,
                    'depth': depth,
                    'target': target,
                    'normalized_eei': normalized_peil,  # Use normalized_eei field for compatibility
                    'angle': angle_str,
                    'vol_wetclay': vol_wetclay
                })

        return all_wells_results, all_wells_data

    def merged_well_analysis(self, las_files, log_keywords_for_finding_cols, target_log_generic_name,
                           depth_ranges, analysis_method, calcmethod, k_method, k_value,
                           alternative_mnemonics=None):
        """
        Perform merged well analysis for EEI, CPEI, or PEIL.

        Args:
            las_files: List of LAS file objects
            log_keywords_for_finding_cols: Dictionary mapping generic names to possible mnemonics
            target_log_generic_name: Generic name of the target log
            depth_ranges: Dictionary mapping well names to (top_depth, bottom_depth) tuples
            analysis_method: 1=EEI, 2=CPEI, 3=PEIL
            calcmethod: Method to use for EEI calculation (only used for EEI)
            k_method: Method for k value (only used for EEI)
            k_value: Constant k value (only used for EEI)
            alternative_mnemonics: Dictionary to track alternative mnemonics selected by the user

        Returns:
            List of well data for plotting
        """
        # Import additional functions from main module that haven't been modularized yet
        from load_multilas_EEI_XCOR_PLOT_Final import (
            calculate_eei, calculate_cpei_for_plotting, calculate_peil_for_plotting,
            validate_cpei_peil_inputs, calculate_cpei, calculate_peil
        )

        logger.info("Starting merged well analysis...")

        merged_depth_list = []
        merged_dt_list = []
        merged_dts_list = []
        merged_rhob_list = []
        merged_target_list = []

        # Initialize alternative_mnemonics if not provided
        if alternative_mnemonics is None:
            alternative_mnemonics = {}

        required_base_generic_logs = ['DEPTH', 'DT', 'DTS', 'RHOB']
        processed_wells_for_merge = 0

        for las in las_files:
            well_name = las.well.WELL.value
            top_depth, bottom_depth = depth_ranges[well_name]

            current_well_cols = find_default_columns(las, log_keywords_for_finding_cols)

            actual_mnemonics_for_merge = {}
            missing_logs_for_this_well_merge = []
            for generic_name in required_base_generic_logs:
                mnemonic = current_well_cols.get(generic_name)
                if mnemonic is None or mnemonic not in las.curves:
                    missing_logs_for_this_well_merge.append(f"{generic_name} (searched aliases: {log_keywords_for_finding_cols.get(generic_name, [])})")
                else:
                    actual_mnemonics_for_merge[generic_name] = mnemonic

            # Check for missing required base logs (these can't be substituted)
            if any(log in missing_logs_for_this_well_merge for log in required_base_generic_logs):
                print(f"Warning: For merged analysis, well {well_name} is missing required base logs: {', '.join(missing_logs_for_this_well_merge)}. Skipping this well's data for merge.")
                continue # Skip this well for merging

            target_actual_mnemonic_for_merge = current_well_cols.get(target_log_generic_name)
            if target_actual_mnemonic_for_merge is None and target_log_generic_name in las.curves: # Direct check
                 target_actual_mnemonic_for_merge = target_log_generic_name
                 print(f"Info: Target log '{target_log_generic_name}' used directly as mnemonic for well {well_name} in merge.")

            # Check if target log is missing and offer interactive fallback selection
            if target_actual_mnemonic_for_merge is None or target_actual_mnemonic_for_merge not in las.curves:
                # Check if we already have an alternative for this well
                well_key = f"{well_name}:{target_log_generic_name}"
                if well_key in alternative_mnemonics:
                    alternative = alternative_mnemonics[well_key]
                    if alternative is not None:
                        target_actual_mnemonic_for_merge = alternative
                        print(f"Info: Using previously selected alternative '{alternative}' for target log '{target_log_generic_name}' in well '{well_name}' for merged analysis.")
                    else:
                        print(f"Info: Well '{well_name}' was previously marked to be skipped for target log '{target_log_generic_name}'. Skipping for merged analysis.")
                        continue
                else:
                    # Display dialog for selecting an alternative
                    print(f"Warning: Target log '{target_log_generic_name}' (resolved mnemonic '{target_actual_mnemonic_for_merge or 'None'}', searched aliases: {log_keywords_for_finding_cols.get(target_log_generic_name, [target_log_generic_name])}) not found in well {well_name}.")
                    alternative = select_alternative_mnemonic(las, target_log_generic_name, well_name)

                    # Store the selected alternative (or None if skipped)
                    alternative_mnemonics[well_key] = alternative

                    if alternative is None:
                        print(f"Info: User chose to skip well '{well_name}' for target log '{target_log_generic_name}' in merged analysis.")
                        continue
                    else:
                        target_actual_mnemonic_for_merge = alternative
                        print(f"Info: Using alternative '{alternative}' for target log '{target_log_generic_name}' in well '{well_name}' for merged analysis.")

            depth_data = np.array(las[actual_mnemonics_for_merge['DEPTH']].data)
            dt_data = np.array(las[actual_mnemonics_for_merge['DT']].data)
            dts_data = np.array(las[actual_mnemonics_for_merge['DTS']].data)
            rhob_data = np.array(las[actual_mnemonics_for_merge['RHOB']].data)
            target_data = np.array(las[target_actual_mnemonic_for_merge].data)

            # Find nearest indices for top and bottom depths
            top_index = find_nearest_index(depth_data, top_depth)
            bottom_index = find_nearest_index(depth_data, bottom_depth)

            # Ensure top_index is smaller than bottom_index
            if top_index > bottom_index:
                top_index, bottom_index = bottom_index, top_index

            # Slice the arrays using the indices
            merged_depth_list.extend(depth_data[top_index:bottom_index+1])
            merged_dt_list.extend(dt_data[top_index:bottom_index+1])
            merged_dts_list.extend(dts_data[top_index:bottom_index+1])
            merged_rhob_list.extend(rhob_data[top_index:bottom_index+1])
            merged_target_list.extend(target_data[top_index:bottom_index+1])
            processed_wells_for_merge += 1

        if processed_wells_for_merge == 0 or not merged_depth_list: # Check if any data was actually merged
            print("No data could be merged for merged analysis (e.g. all wells missed required logs). Cannot proceed with merged optimum angle calculation.")
            return [] # Return empty list for all_wells_data

        merged_depth = np.array(merged_depth_list)
        merged_dt = np.array(merged_dt_list)
        merged_dts = np.array(merged_dts_list)
        merged_rhob = np.array(merged_rhob_list)
        merged_target = np.array(merged_target_list)

        # Validate merged data before proceeding with optimization
        if any(x is None for x in [merged_depth, merged_dt, merged_dts, merged_rhob, merged_target]) or \
           any(not x.size for x in [merged_depth, merged_dt, merged_dts, merged_rhob, merged_target] if x is not None and hasattr(x, 'size')):
            logger.error("Merged data arrays (depth, dt, dts, rhob, or target) are None or empty after internal merging. Aborting merged optimization.")
            return [] # Return empty list, consistent with other failure paths
        logger.info(f"Merged data successfully created internally from {processed_wells_for_merge} wells. Total points: {len(merged_depth)}. Proceeding with optimization...")

        # Convert slowness to velocity (assuming microseconds/ft to m/s conversion)
        pvel = 304800 / merged_dt  # Convert microseconds/ft to m/s
        svel = 304800 / merged_dts  # Convert microseconds/ft to m/s

        # Determine k value based on method selected
        if k_method == 1:
            # Calculate k from velocity ratio
            velocity_ratio = svel / pvel
            k = np.nanmean(velocity_ratio**2)

            # Validate calculated k value
            if k is None or not np.isfinite(k) or k <= 0:
                logger.warning(f"Invalid calculated k value: {k}, using default k=0.25")
                k = 0.25  # Default reasonable k value

            logger.info(f"Calculated k value from logs (merged): {safe_format_float(k, precision=4)}")
        else:
            k = k_value

            # Validate provided k value
            if k is None or not np.isfinite(k) or k <= 0:
                logger.warning(f"Invalid provided k value: {k}, using default k=0.25")
                k = 0.25  # Default reasonable k value

            logger.info(f"Using constant k value (merged): {safe_format_float(k, precision=4)}")

        if analysis_method == 1:  # EEI Analysis
            angles = range(-90, 91)
            correlations = []

            for angle in angles:
                try:
                    eei, _, _ = eeimpcalc(pvel, svel, merged_rhob, angle, k, calcmethod=calcmethod)

                    # Validate EEI calculation result
                    if eei is None or not hasattr(eei, '__len__'):
                        correlation = np.nan
                    else:
                        correlation = nanaware_corrcoef(eei, merged_target)

                    correlations.append(correlation)

                except Exception as e:
                    logger.warning(f"Error calculating EEI for angle {angle} in merged analysis: {str(e)}")
                    correlations.append(np.nan)

            # Find the optimum angle
            correlations = np.array(correlations)
            valid_correlations = correlations[np.isfinite(correlations)]

            if len(valid_correlations) == 0:
                logger.error("All correlations for merged data are NaN. Unable to find optimum angle for merged wells.")
                optimum_angle_merged = 0 # Default or placeholder
                max_correlation_merged = np.nan
            else:
                max_idx = np.nanargmax(correlations)
                optimum_angle_merged = angles[max_idx]
                max_correlation_merged = correlations[max_idx]

            print(f"Optimum angle for Merged Wells: {optimum_angle_merged}°")
            print(f"Maximum correlation coefficient with {target_log_generic_name}: {safe_format_float(max_correlation_merged, precision=4, default='N/A')}")

            # Plot EEI-Target Log Correlation vs Angle using modular plotting component
            plot_correlation_vs_angle(angles, correlations, optimum_angle_merged, target_log_generic_name)

        elif analysis_method == 2:  # CPEI Analysis
            print("Starting CPEI optimization for merged wells...")

            # Define parameter ranges for CPEI
            n_values = np.arange(0.1, 2.1, 0.1)  # n from 0.1 to 2.0 with step 0.1
            phi_values = range(-90, 91)  # phi from -90° to +90°

            correlation_matrix = np.full((len(n_values), len(phi_values)), np.nan)

            for i, n in enumerate(n_values):
                for j, phi in enumerate(phi_values):
                    try:
                        # Validate inputs before calculation
                        validation = validate_cpei_peil_inputs(pvel, svel, merged_rhob, n, phi, "CPEI")
                        if not validation['valid']:
                            logger.warning(f"CPEI validation failed for merged data n={safe_format_float(n, 1)}, phi={phi}: {'; '.join(validation['errors'])}")
                            correlation_matrix[i, j] = np.nan
                            continue

                        cpei = calculate_cpei(pvel, svel, merged_rhob, n, phi)

                        # Validate CPEI output
                        if cpei is None:
                            logger.warning(f"CPEI calculation returned None for merged data n={safe_format_float(n, 1)}, phi={phi}")
                            correlation_matrix[i, j] = np.nan
                            continue

                        if hasattr(cpei, 'size') and cpei.size == 0:
                            logger.warning(f"CPEI calculation returned empty array for merged data n={safe_format_float(n, 1)}, phi={phi}")
                            correlation_matrix[i, j] = np.nan
                            continue

                        correlation = nanaware_corrcoef(cpei, merged_target)
                        correlation_matrix[i, j] = correlation

                    except Exception as e:
                        error_msg = f"Error calculating CPEI for merged data n={safe_format_float(n, 1)}, phi={phi}: {str(e)}"
                        logger.error(error_msg)
                        print(f"Warning: {error_msg}")
                        correlation_matrix[i, j] = np.nan

            # Find optimal parameters
            if np.all(np.isnan(correlation_matrix)):
                print("All correlations are NaN. Unable to find optimum parameters for merged CPEI.")
                optimal_n_merged = 1.0
                optimal_phi_merged = 0
                max_correlation_merged = np.nan
            else:
                max_idx = np.unravel_index(np.nanargmax(correlation_matrix), correlation_matrix.shape)
                optimal_n_merged = n_values[max_idx[0]]
                optimal_phi_merged = phi_values[max_idx[1]]
                max_correlation_merged = correlation_matrix[max_idx]

            print(f"CPEI optimization complete for merged wells:")
            # Safely format merged CPEI parameters using helper functions
            print(f"  Optimal n: {safe_format_float(optimal_n_merged, precision=1, default='N/A')}")

            optimal_phi_merged_val_cpei = safe_format_float(optimal_phi_merged, precision=0, default="N/A")
            phi_display_cpei = f"{optimal_phi_merged_val_cpei}°" if optimal_phi_merged_val_cpei != "N/A" else "N/A"
            print(f"  Optimal phi: {phi_display_cpei}")

            print(f"  Maximum correlation coefficient with {target_log_generic_name}: {safe_format_float(max_correlation_merged, precision=4, default='N/A')}")

            # Plot CPEI correlation heatmap for merged wells using modular plotting component
            plot_correlation_heatmap(correlation_matrix, phi_values, n_values, optimal_n_merged, optimal_phi_merged,
                                   max_correlation_merged, target_log_generic_name, 'CPEI')

            # Store parameters as string for compatibility with safe formatting
            optimum_angle_merged = safe_format_parameter_string(optimal_n_merged, optimal_phi_merged, default=f"n={str(optimal_n_merged)}, phi={str(optimal_phi_merged)}")

        elif analysis_method == 3:  # PEIL Analysis
            print("Starting PEIL optimization for merged wells...")

            # Define parameter ranges for PEIL
            n_values = np.arange(0.1, 2.1, 0.1)  # n from 0.1 to 2.0 with step 0.1
            phi_values = range(-90, 91)  # phi from -90° to +90°

            correlation_matrix = np.full((len(n_values), len(phi_values)), np.nan)

            for i, n in enumerate(n_values):
                for j, phi in enumerate(phi_values):
                    try:
                        # Validate inputs before calculation
                        validation = validate_cpei_peil_inputs(pvel, svel, merged_rhob, n, phi, "PEIL")
                        if not validation['valid']:
                            logger.warning(f"PEIL validation failed for merged data n={safe_format_float(n, 1)}, phi={phi}: {'; '.join(validation['errors'])}")
                            correlation_matrix[i, j] = np.nan
                            continue

                        peil = calculate_peil(pvel, svel, merged_rhob, n, phi)

                        # Validate PEIL output
                        if peil is None:
                            logger.warning(f"PEIL calculation returned None for merged data n={safe_format_float(n, 1)}, phi={phi}")
                            correlation_matrix[i, j] = np.nan
                            continue

                        if hasattr(peil, 'size') and peil.size == 0:
                            logger.warning(f"PEIL calculation returned empty array for merged data n={safe_format_float(n, 1)}, phi={phi}")
                            correlation_matrix[i, j] = np.nan
                            continue

                        correlation = nanaware_corrcoef(peil, merged_target)
                        correlation_matrix[i, j] = correlation

                    except Exception as e:
                        error_msg = f"Error calculating PEIL for merged data n={safe_format_float(n, 1)}, phi={phi}: {str(e)}"
                        logger.error(error_msg)
                        print(f"Warning: {error_msg}")
                        correlation_matrix[i, j] = np.nan

            # Find optimal parameters
            if np.all(np.isnan(correlation_matrix)):
                print("All correlations are NaN. Unable to find optimum parameters for merged PEIL.")
                optimal_n_merged = 1.0
                optimal_phi_merged = 0
                max_correlation_merged = np.nan
            else:
                max_idx = np.unravel_index(np.nanargmax(correlation_matrix), correlation_matrix.shape)
                optimal_n_merged = n_values[max_idx[0]]
                optimal_phi_merged = phi_values[max_idx[1]]
                max_correlation_merged = correlation_matrix[max_idx]

            print(f"PEIL optimization complete for merged wells:")
            # Safely format merged PEIL parameters with None value checks
            # Safely format merged PEIL parameters using helper functions
            print(f"  Optimal n: {safe_format_float(optimal_n_merged, precision=1, default='N/A')}")

            optimal_phi_merged_val_peil = safe_format_float(optimal_phi_merged, precision=0, default="N/A")
            phi_display_peil = f"{optimal_phi_merged_val_peil}°" if optimal_phi_merged_val_peil != "N/A" else "N/A"
            print(f"  Optimal phi: {phi_display_peil}")

            print(f"  Maximum correlation coefficient with {target_log_generic_name}: {safe_format_float(max_correlation_merged, precision=4, default='N/A')}")

            # Plot PEIL correlation heatmap for merged wells using modular plotting component
            plot_correlation_heatmap(correlation_matrix, phi_values, n_values, optimal_n_merged, optimal_phi_merged,
                                   max_correlation_merged, target_log_generic_name, 'PEIL')

            # Store parameters as string for compatibility with safe formatting
            optimum_angle_merged = safe_format_parameter_string(optimal_n_merged, optimal_phi_merged, default=f"n={str(optimal_n_merged)}, phi={str(optimal_phi_merged)}")

        # Calculate analysis results using the (merged) optimum parameters for each well
        all_wells_data_output = []
        for las in las_files: # Iterate through original las_files to generate output for each
            well_name = las.well.WELL.value
            top_depth, bottom_depth = depth_ranges[well_name]

            current_well_cols = find_default_columns(las, log_keywords_for_finding_cols)

            base_mnemonics = {}
            missing_calc_logs = []
            for generic_name in required_base_generic_logs:
                mnemonic = current_well_cols.get(generic_name)
                if mnemonic is None or mnemonic not in las.curves:
                    missing_calc_logs.append(f"{generic_name} (searched: {log_keywords_for_finding_cols.get(generic_name, [])})")
                else:
                    base_mnemonics[generic_name] = mnemonic

            target_actual_mnemonic = current_well_cols.get(target_log_generic_name)
            if target_actual_mnemonic is None and target_log_generic_name in las.curves:
                target_actual_mnemonic = target_log_generic_name
                analysis_type_name = "EEI" if analysis_method == 1 else ("CPEI" if analysis_method == 2 else "PEIL")
                print(f"Info: Target log '{target_log_generic_name}' used directly for {analysis_type_name} calc in well {well_name}.")

            if target_actual_mnemonic is None or target_actual_mnemonic not in las.curves:
                missing_calc_logs.append(f"{target_log_generic_name} (resolved mnemonic '{target_actual_mnemonic or 'None'}', searched: {log_keywords_for_finding_cols.get(target_log_generic_name, [target_log_generic_name])})")

            vcl_actual_mnemonic = current_well_cols.get('VCL') # Optional

            if missing_calc_logs:
                analysis_type_name = "EEI" if analysis_method == 1 else ("CPEI" if analysis_method == 2 else "PEIL")
                print(f"Warning: For merged {analysis_type_name} calculation plotting, well {well_name} is missing logs: {', '.join(missing_calc_logs)}. Appending placeholder data for this well.")
                all_wells_data_output.append({
                    'well_name': well_name, 'depth': None, 'target': None,
                    'normalized_eei': None, 'angle': optimum_angle_merged, 'vol_wetclay': None
                })
                continue

            if analysis_method == 1:  # EEI Analysis
                depth_res, target_res, norm_eei_res, vcl_res = calculate_eei(
                    las,
                    base_mnemonics,
                    target_actual_mnemonic,
                    vcl_actual_mnemonic,
                    top_depth, bottom_depth,
                    optimum_angle_merged, # Use the optimum_angle derived from merged data
                    calcmethod,
                    k_method,
                    k_value
                )

                all_wells_data_output.append({
                    'well_name': well_name,
                    'depth': depth_res,
                    'target': target_res,
                    'normalized_eei': norm_eei_res,
                    'angle': optimum_angle_merged, # Store the angle used
                    'vol_wetclay': vcl_res
                })

            elif analysis_method == 2:  # CPEI Analysis
                # Calculate CPEI using optimal parameters for plotting
                depth_res, target_res, norm_cpei_res, vcl_res = calculate_cpei_for_plotting(
                    las,
                    base_mnemonics,
                    target_actual_mnemonic,
                    vcl_actual_mnemonic,
                    top_depth, bottom_depth,
                    optimal_n_merged, optimal_phi_merged
                )

                all_wells_data_output.append({
                    'well_name': well_name,
                    'depth': depth_res,
                    'target': target_res,
                    'normalized_eei': norm_cpei_res,  # Use normalized_eei field for compatibility
                    'angle': optimum_angle_merged,  # Store the parameters used
                    'vol_wetclay': vcl_res
                })

            elif analysis_method == 3:  # PEIL Analysis
                # Calculate PEIL using optimal parameters for plotting
                depth_res, target_res, norm_peil_res, vcl_res = calculate_peil_for_plotting(
                    las,
                    base_mnemonics,
                    target_actual_mnemonic,
                    vcl_actual_mnemonic,
                    top_depth, bottom_depth,
                    optimal_n_merged, optimal_phi_merged
                )

                all_wells_data_output.append({
                    'well_name': well_name,
                    'depth': depth_res,
                    'target': target_res,
                    'normalized_eei': norm_peil_res,  # Use normalized_eei field for compatibility
                    'angle': optimum_angle_merged,  # Store the parameters used
                    'vol_wetclay': vcl_res
                })

        return all_wells_data_output

    def run_eei_analysis(self):
        """
        Run the complete EEI analysis workflow.

        Returns:
            bool: True if user wants to restart, False if user wants to exit
        """
        logger.info("Starting EEI analysis workflow...")

        # Load LAS files
        las_files = load_multiple_las_files()
        if not las_files:
            print("No LAS files loaded. Exiting.")
            return False

        # Validate essential logs
        validation_results = validate_essential_logs(las_files, LOG_KEYWORDS)
        validation_summary = generate_validation_report(validation_results)

        # Ask user if they want to continue despite missing logs
        if validation_summary['invalid_files'] > 0:
            continue_choice = messagebox.askyesno(
                "Missing Essential Logs",
                f"{validation_summary['invalid_files']} out of {validation_summary['total_files']} files are missing essential logs. Continue anyway?"
            )
            if not continue_choice:
                print("Analysis cancelled by user. Exiting.")
                return False

        # Categorize and display log inventory
        categorized_logs = categorize_log_curves(las_files)
        display_log_inventory(categorized_logs)

        # Log available curves for each well (for debugging)
        for las in las_files:
            log_available_curves(las)

        # Get analysis type and parameters from user
        analysis_result = get_analysis_type_and_parameters()
        if analysis_result is None:
            print("Analysis cancelled by user.")
            return False

        analysis_method, calcmethod, k_method, k_value = analysis_result

        # Prompt user to load Excel file with depth ranges upfront
        # Pass the LAS files to filter the Excel data to only include matching wells
        preloaded_excel_df = load_excel_depth_ranges(las_files)

        # Provide feedback if Excel data was loaded
        if preloaded_excel_df is not None:
            print(f"Excel file with depth ranges loaded successfully.")
            print(f"Found {len(preloaded_excel_df)} boundary entries for {preloaded_excel_df['Well'].nunique()} wells.")
            print("This data will be automatically used in the depth ranges dialog.")

        # Get depth ranges, passing the preloaded Excel data if available
        depth_ranges = get_depth_ranges(las_files, LOG_KEYWORDS, preloaded_excel_df)
        if not depth_ranges:
            print("No depth ranges specified. Exiting.")
            return False

        # Get target log from user
        target_log_generic_name = get_target_log()
        if not target_log_generic_name:
            print("No target log specified. Exiting.")
            return False

        # Set up log keywords for finding columns
        log_keywords_for_finding_cols = {
            'DEPTH': ['DEPTH', 'DEPT', 'MD', 'TVDSS'],
            'DT': ['DT', 'AC', 'DTCO'],
            'DTS': ['DTS', 'DTSM', 'DTSH'],
            'RHOB': ['RHOB', 'RHOZ', 'ZDEN'],
            'VCL': ['VCL', 'VSH', 'VCLAY']
        }

        # Add target log keywords if not already present
        if target_log_generic_name not in log_keywords_for_finding_cols:
            log_keywords_for_finding_cols[target_log_generic_name] = [target_log_generic_name]

        # Analyze log availability
        analyze_log_availability(las_files)

        # Initialize alternative mnemonics tracking
        alternative_mnemonics = {}

        # Determine analysis workflow (individual vs merged)
        workflow_choice = messagebox.askyesnocancel(
            "Analysis Workflow",
            "Choose analysis workflow:\n\n"
            "Yes: Individual well analysis (optimize parameters for each well separately)\n"
            "No: Merged well analysis (optimize parameters using combined data from all wells)\n"
            "Cancel: Exit"
        )

        if workflow_choice is None:  # Cancel
            print("Analysis cancelled by user.")
            return False

        # Execute analysis workflow
        if workflow_choice:  # Individual well analysis
            print("Starting individual well analysis...")
            all_wells_results, all_wells_data = self.individual_well_analysis(
                las_files, log_keywords_for_finding_cols, target_log_generic_name,
                depth_ranges, analysis_method, calcmethod, k_method, k_value,
                alternative_mnemonics
            )
        else:  # Merged well analysis
            print("Starting merged well analysis...")
            all_wells_data = self.merged_well_analysis(
                las_files, log_keywords_for_finding_cols, target_log_generic_name,
                depth_ranges, analysis_method, calcmethod, k_method, k_value,
                alternative_mnemonics
            )
            all_wells_results = []  # Not used in merged analysis

        # Check if analysis produced valid results
        if not all_wells_data:
            print("No valid analysis results generated. Analysis failed.")
            return False

        # Plot results using modular plotting component
        analysis_type_name = "EEI" if analysis_method == 1 else ("CPEI" if analysis_method == 2 else "PEIL")
        print(f"Plotting {analysis_type_name} vs {target_log_generic_name} results...")

        plot_eei_vs_target(all_wells_data, target_log_generic_name, analysis_type_name)

        # Show next action dialog
        next_action = show_next_action_dialog()

        if next_action == "calculator":
            # Launch calculator interface
            calculator_result = get_calculations_for_eei()
            if calculator_result:
                print("Calculator calculations completed successfully.")
            else:
                print("Calculator was cancelled or failed.")

        elif next_action == "restart":
            print("Restarting analysis...")
            return True  # Signal to restart

        elif next_action == "exit":
            print("Analysis completed. Exiting.")
            return False  # Signal to exit

        else:
            print("Analysis completed.")
            return False  # Default to exit


# Create a global instance of the workflow orchestrator
_workflow_orchestrator = WorkflowOrchestrator()

# Module-level functions that delegate to the orchestrator instance
def individual_well_analysis(las_files, log_keywords_for_finding_cols, target_log_generic_name,
                           depth_ranges, analysis_method, calcmethod, k_method, k_value,
                           alternative_mnemonics=None):
    """Module-level function for individual well analysis."""
    return _workflow_orchestrator.individual_well_analysis(
        las_files, log_keywords_for_finding_cols, target_log_generic_name,
        depth_ranges, analysis_method, calcmethod, k_method, k_value,
        alternative_mnemonics
    )

def merged_well_analysis(las_files, log_keywords_for_finding_cols, target_log_generic_name,
                       depth_ranges, analysis_method, calcmethod, k_method, k_value,
                       alternative_mnemonics=None):
    """Module-level function for merged well analysis."""
    return _workflow_orchestrator.merged_well_analysis(
        las_files, log_keywords_for_finding_cols, target_log_generic_name,
        depth_ranges, analysis_method, calcmethod, k_method, k_value,
        alternative_mnemonics
    )

def run_eei_analysis():
    """Module-level function for running the complete EEI analysis workflow."""
    return _workflow_orchestrator.run_eei_analysis()
